package com.zbkj.common.constants;

/**
 * 中国日历相关常量
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
public class CalendarConstants {

    // ==================== 节假日API配置 ====================

    /**
     * 节假日API地址模板
     * {version} - API版本号
     * {year} - 年份
     */
    public static final String HOLIDAY_API_URL = "https://unpkg.com/holiday-calendar@{version}/data/CN/{year}.json";

    /**
     * 默认API版本
     */
    public static final String DEFAULT_VERSION = "1.1.9";

    // ==================== 缓存配置 ====================

    /**
     * 节假日日历缓存键模板
     */
    public static final String CACHE_KEY_HOLIDAY_CALENDAR = "holiday:calendar:{}";

    /**
     * 日期类型缓存键模板
     */
    public static final String CACHE_KEY_DATE_TYPE = "holiday:date_type:{}";

    /**
     * 价格缓存键模板
     */
    public static final String CACHE_KEY_HOTEL_PRICE = "hotel:price:{}:{}";

    /**
     * 缓存过期时间（小时）
     */
    public static final int CACHE_EXPIRE_HOURS = 24;

    /**
     * 本地缓存过期时间（分钟）
     */
    public static final int LOCAL_CACHE_EXPIRE_MINUTES = 60;

    // ==================== 日期类型常量 ====================

    /**
     * 工作日
     */
    public static final int DATE_TYPE_WORKDAY = 1;

    /**
     * 周末
     */
    public static final int DATE_TYPE_WEEKEND = 2;

    /**
     * 节假日
     */
    public static final int DATE_TYPE_HOLIDAY = 3;

    /**
     * 调休补班
     */
    public static final int DATE_TYPE_TRANSFER_WORKDAY = 4;

    // ==================== 节假日类型 ======a==============

    /**
     * 法定节假日
     */
    public static final String HOLIDAY_TYPE_PUBLIC = "public_holiday";

    /**
     * 调休工作日
     */
    public static final String HOLIDAY_TYPE_TRANSFER_WORKDAY = "transfer_workday";

    // ==================== 查询限制 ====================

    /**
     * 最大查询天数范围
     */
    public static final int MAX_QUERY_DAYS = 365;

    /**
     * 批量查询最大数量
     */
    public static final int MAX_BATCH_SIZE = 100;

    // ==================== 错误信息 ====================

    /**
     * 日期格式错误
     */
    public static final String ERROR_DATE_FORMAT = "日期格式错误，请使用yyyy-MM-dd格式";

    /**
     * 查询范围超限
     */
    public static final String ERROR_QUERY_RANGE_EXCEED = "查询范围不能超过365天";

    /**
     * 获取节假日数据失败
     */
    public static final String ERROR_FETCH_HOLIDAY_DATA = "获取节假日数据失败";
}
