package com.zbkj.common.constants;

/**
 * 酒店相关常量类
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
public class HotelConstants {

    // ==================== 酒店商品相关常量 ====================

    /**
     * 酒店商品类型
     */
    public static final Integer HOTEL_PRODUCT_TYPE = 7; // 酒店房间商品

    /**
     * 酒店商品配送方式
     */
    public static final String HOTEL_DELIVERY_METHOD = "2"; // 到店核销

    /**
     * 酒店分类名称
     */
    public static final String HOTEL_CATEGORY_NAME = "酒店预订";

    /**
     * 酒店商品单位
     */
    public static final String HOTEL_PRODUCT_UNIT = "间/晚";

    /**
     * 酒店商品默认图片
     */
    public static final String HOTEL_DEFAULT_IMAGE = "/static/images/hotel_default.jpg";

    // ==================== 价格策略常量 ====================

    /**
     * 价格策略类型：基础价格(工作日)
     */
    public static final Integer PRICE_STRATEGY_WORKDAY = 1;

    /**
     * 价格策略类型：周末价格
     */
    public static final Integer PRICE_STRATEGY_WEEKEND = 2;

    /**
     * 价格策略类型：节假日价格
     */
    public static final Integer PRICE_STRATEGY_HOLIDAY = 3;

    /**
     * 价格策略类型：按日期范围
     */
    public static final Integer PRICE_STRATEGY_DATE_RANGE = 4;

    /**
     * 价格策略类型：按具体日期
     */
    public static final Integer PRICE_STRATEGY_SPECIFIC_DATE = 5;

    /**
     * 价格策略类型：特价策略
     */
    public static final Integer PRICE_STRATEGY_SPECIAL_OFFER = 6;

    // ==================== 房间状态常量 ====================

    /**
     * 房间状态：禁用
     */
    public static final Integer ROOM_STATUS_DISABLED = 0;

    /**
     * 房间状态：启用
     */
    public static final Integer ROOM_STATUS_ENABLED = 1;

    // ==================== 同步任务常量 ====================

    /**
     * 默认生成天数
     */
    public static final Integer DEFAULT_GENERATE_DAYS = 30;

    /**
     * 过期清理天数
     */
    public static final Integer DEFAULT_EXPIRED_DAYS = 30;

    /**
     * 任务类型：同步
     */
    public static final String TASK_TYPE_SYNC = "sync";

    /**
     * 任务类型：清理
     */
    public static final String TASK_TYPE_CLEAN = "clean";

    /**
     * 任务状态：执行中
     */
    public static final Integer TASK_STATUS_RUNNING = 0;

    /**
     * 任务状态：成功
     */
    public static final Integer TASK_STATUS_SUCCESS = 1;

    /**
     * 任务状态：失败
     */
    public static final Integer TASK_STATUS_FAILED = 2;

    // ==================== 分销相关常量 ====================

    /**
     * 默认一级分佣比例（%）
     */
    public static final Integer DEFAULT_BROKERAGE_FIRST = 10;

    /**
     * 默认二级分佣比例（%）
     */
    public static final Integer DEFAULT_BROKERAGE_SECOND = 5;

    /**
     * 分佣类型：不参与分佣
     */
    public static final Integer BROKERAGE_TYPE_NONE = 0;

    /**
     * 分佣类型：单独分佣
     */
    public static final Integer BROKERAGE_TYPE_INDIVIDUAL = 1;

    /**
     * 分佣类型：默认分佣
     */
    public static final Integer BROKERAGE_TYPE_DEFAULT = 2;

    // ==================== SKU相关常量 ====================

    /**
     * 酒店SKU前缀
     */
    public static final String HOTEL_SKU_PREFIX = "HOTEL_";

    /**
     * SKU分隔符
     */
    public static final String SKU_SEPARATOR = "_";

    // ==================== 系统配置键常量 ====================

    /**
     * 酒店商品同步开关
     */
    public static final String CONFIG_HOTEL_SYNC_ENABLED = "hotel_product_sync_enabled";

    /**
     * 酒店商品生成天数
     */
    public static final String CONFIG_HOTEL_GENERATE_DAYS = "hotel_product_generate_days";

    /**
     * 酒店过期商品清理天数
     */
    public static final String CONFIG_HOTEL_EXPIRED_DAYS = "hotel_product_expired_days";

    /**
     * 酒店默认图片
     */
    public static final String CONFIG_HOTEL_DEFAULT_IMAGE = "hotel_default_image";

    /**
     * 酒店一级分佣比例
     */
    public static final String CONFIG_HOTEL_BROKERAGE_FIRST = "hotel_brokerage_first";

    /**
     * 酒店二级分佣比例
     */
    public static final String CONFIG_HOTEL_BROKERAGE_SECOND = "hotel_brokerage_second";

    // ==================== 商品名称格式常量 ====================

    /**
     * 商品名称分隔符
     */
    public static final String PRODUCT_NAME_SEPARATOR = "-";

    /**
     * 日期格式
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * SKU日期格式
     */
    public static final String SKU_DATE_FORMAT = "yyyyMMdd";

    // ==================== 价格计算常量 ====================

    /**
     * 市场价倍数
     */
    public static final String MARKET_PRICE_RATIO = "1.2";

    /**
     * 成本价倍数
     */
    public static final String COST_PRICE_RATIO = "0.8";

    // ==================== 错误信息常量 ====================

    /**
     * 房间不存在错误
     */
    public static final String ERROR_ROOM_NOT_FOUND = "房间不存在或未启用";

    /**
     * 商户不存在错误
     */
    public static final String ERROR_MERCHANT_NOT_FOUND = "商户不存在";

    /**
     * 价格策略不存在错误
     */
    public static final String ERROR_PRICE_STRATEGY_NOT_FOUND = "价格策略不存在";

    /**
     * 分类创建失败错误
     */
    public static final String ERROR_CATEGORY_CREATE_FAILED = "分类创建失败";

    /**
     * 商品同步失败错误
     */
    public static final String ERROR_PRODUCT_SYNC_FAILED = "商品同步失败";

    // ==================== 日志相关常量 ====================

    /**
     * 同步开始日志
     */
    public static final String LOG_SYNC_START = "=== 开始执行酒店商品同步任务 ===";

    /**
     * 同步完成日志
     */
    public static final String LOG_SYNC_COMPLETE = "=== 酒店商品同步任务执行完成 ===";

    /**
     * 清理开始日志
     */
    public static final String LOG_CLEAN_START = "=== 开始执行酒店过期商品清理任务 ===";

    /**
     * 清理完成日志
     */
    public static final String LOG_CLEAN_COMPLETE = "=== 酒店过期商品清理任务执行完成 ===";

    // ==================== 工具方法 ====================

    /**
     * 生成酒店SKU
     *
     * @param merchantId 商户ID
     * @param roomId 房间ID
     * @param dateStr 日期字符串(yyyyMMdd格式)
     * @return SKU编码
     */
    public static String generateHotelSku(Integer merchantId, Integer roomId, String dateStr) {
        return HOTEL_SKU_PREFIX + merchantId + SKU_SEPARATOR + roomId + SKU_SEPARATOR + dateStr;
    }

    /**
     * 生成商品名称
     *
     * @param hotelName 酒店名称
     * @param roomName 房间名称
     * @param dateStr 日期字符串(yyyy-MM-dd格式)
     * @return 商品名称
     */
    public static String generateProductName(String hotelName, String roomName, String dateStr) {
        return hotelName + PRODUCT_NAME_SEPARATOR + roomName + PRODUCT_NAME_SEPARATOR + dateStr;
    }

    /**
     * 判断是否为酒店商品
     *
     * @param productType 商品类型
     * @param deliveryMethod 配送方式
     * @return 是否为酒店商品
     */
    public static boolean isHotelProduct(Integer productType, String deliveryMethod) {
        return HOTEL_PRODUCT_TYPE.equals(productType) && HOTEL_DELIVERY_METHOD.equals(deliveryMethod);
    }
}
