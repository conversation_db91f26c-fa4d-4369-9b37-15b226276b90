package com.zbkj.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.Builder;

import java.io.Serializable;

/**
 * 节假日响应
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Builder
@ApiModel(value = "HolidayResponse对象", description = "节假日响应")
public class HolidayResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日期（格式：yyyy-MM-dd）")
    private String date;

    @ApiModelProperty(value = "节假日中文名称")
    private String name;

    @ApiModelProperty(value = "节假日英文名称")
    private String nameEn;

    @ApiModelProperty(value = "节假日类型（public_holiday-法定节假日，transfer_workday-调休工作日）")
    private String type;

    @ApiModelProperty(value = "是否为法定节假日")
    private Boolean isPublicHoliday;

    @ApiModelProperty(value = "是否为调休工作日")
    private Boolean isTransferWorkday;
}
