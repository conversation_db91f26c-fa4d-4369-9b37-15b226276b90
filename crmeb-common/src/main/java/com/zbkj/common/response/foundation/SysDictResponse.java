package com.zbkj.common.response.foundation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "SysDictResponse", description = "字典返回对象")
public class SysDictResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典子项的信息
     */
    @ApiModelProperty(value = "字典子项的信息")
    private List<SysDictItemResponse> itemList;

    /**
     * 字典的对象结果
     */
    @ApiModelProperty(value = "字典的对象结果")
    private Map<String, String> dictMap;
}
