package com.zbkj.common.response.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 酒店房型响应对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelRoomResponse", description = "酒店房型响应对象")
public class HotelRoomResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房间ID")
    private Integer id;

    @ApiModelProperty(value = "商户ID(酒店ID)")
    private Integer merId;

    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "房型类型(标准间,大床房,套房等)")
    private String roomType;

    @ApiModelProperty(value = "房间设施(JSON格式)")
    private String roomFacilities;

    @ApiModelProperty(value = "房间面积(平方米)")
    private BigDecimal roomArea;

    @ApiModelProperty(value = "楼层信息")
    private String roomFloor;

    @ApiModelProperty(value = "床型(单人床,双人床,大床等)")
    private String bedType;

    @ApiModelProperty(value = "最大入住人数")
    private Integer maxGuests;

    @ApiModelProperty(value = "房型基础价格")
    private BigDecimal basePrice;

    @ApiModelProperty(value = "总房间数")
    private Integer totalRooms;

    @ApiModelProperty(value = "房间图片(JSON格式)")
    private String roomImages;

    @ApiModelProperty(value = "房间描述")
    private String roomDescription;

    @ApiModelProperty(value = "最大可预订天数")
    private Integer maxBookingDays;

    @ApiModelProperty(value = "状态:0-禁用,1-启用")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否特价房源:0-否,1-是")
    private Integer isSpecialOffer;

    @ApiModelProperty(value = "周末不加价:0-否,1-是")
    private Integer weekendNoMarkup;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "价格策略数量")
    private Integer priceStrategyCount;

    @ApiModelProperty(value = "已生成商品数量")
    private Integer productCount;
}
