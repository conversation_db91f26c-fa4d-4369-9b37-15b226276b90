package com.zbkj.common.response.hotel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 酒店价格策略响应对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelPriceStrategyResponse", description = "酒店价格策略响应对象")
public class HotelPriceStrategyResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "策略ID")
    private Integer id;

    @ApiModelProperty(value = "商户ID(酒店ID)")
    private Integer merId;

    @ApiModelProperty(value = "房间ID")
    private Integer roomId;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "策略类型:1-基础价格(工作日),2-周末价格,3-节假日价格,4-按日期范围,5-按具体日期,6-特价策略")
    private Integer strategyType;

    @ApiModelProperty(value = "策略类型描述")
    private String strategyTypeDesc;

    @ApiModelProperty(value = "适用星期(1,2,3,4,5,6,7 逗号分隔)")
    private String weekDays;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "具体日期(JSON格式)")
    private String specificDates;

    @ApiModelProperty(value = "价格值(固定价格)")
    private BigDecimal priceValue;

    @ApiModelProperty(value = "优先级(数值越大优先级越高)")
    private Integer priority;

    @ApiModelProperty(value = "状态:0-禁用,1-启用")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "策略描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
