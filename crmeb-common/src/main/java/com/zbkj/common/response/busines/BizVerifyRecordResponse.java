package com.zbkj.common.response.busines;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: BizVerifyRecordResponse
 * @Description: 龙宫币全部记录对象
 * @Author: zlj
 * @Date: 2025-06-03 18:09
 * @Version: 1.0
 **/
@Data
public class BizVerifyRecordResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 热点或名称
     */
    @ApiModelProperty(value = "热点或名称")
    private String name;
    /**
     * 获取的热点或名称
     */
    @ApiModelProperty(value = "获取的热点或名称")
    private String hotspotName;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer number;
    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String userName;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "时间")
    private Date time;
    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String source;
}
