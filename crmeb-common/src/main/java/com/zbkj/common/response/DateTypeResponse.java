package com.zbkj.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.Builder;

import java.io.Serializable;

/**
 * 日期类型响应
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Builder
@ApiModel(value = "DateTypeResponse对象", description = "日期类型响应")
public class DateTypeResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日期（格式：yyyy-MM-dd）")
    private String date;

    @ApiModelProperty(value = "日期类型代码（1-工作日，2-周末，3-节假日，4-调休补班）")
    private Integer type;

    @ApiModelProperty(value = "日期类型名称")
    private String typeName;

    @ApiModelProperty(value = "是否为节假日")
    private Boolean isHoliday;

    @ApiModelProperty(value = "是否为周末")
    private Boolean isWeekend;

    @ApiModelProperty(value = "是否为工作日（包含调休补班）")
    private Boolean isWorkday;

    @ApiModelProperty(value = "节假日名称（如果是节假日）")
    private String holidayName;

    @ApiModelProperty(value = "备注信息")
    private String remark;
}
