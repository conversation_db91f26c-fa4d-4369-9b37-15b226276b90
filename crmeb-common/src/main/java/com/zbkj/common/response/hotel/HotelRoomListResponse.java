package com.zbkj.common.response.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 酒店房型列表响应对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelRoomListResponse", description = "酒店房型列表响应对象")
public class HotelRoomListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房型ID")
    private Integer roomId;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "房型类型")
    private String roomType;

    @ApiModelProperty(value = "房型图片列表")
    private List<String> roomImages;

    @ApiModelProperty(value = "房间面积")
    private BigDecimal roomArea;

    @ApiModelProperty(value = "床型")
    private String bedType;

    @ApiModelProperty(value = "最大入住人数")
    private Integer maxGuests;

    @ApiModelProperty(value = "房间设施列表")
    private List<String> facilities;

    @ApiModelProperty(value = "价格信息")
    private PriceInfo priceInfo;

    @ApiModelProperty(value = "可用性信息")
    private AvailabilityInfo availability;

    @ApiModelProperty(value = "房间描述")
    private String roomDescription;

    @ApiModelProperty(value = "是否特价房源:0-否,1-是")
    private Integer isSpecialOffer;

    @ApiModelProperty(value = "周末不加价:0-否,1-是")
    private Integer weekendNoMarkup;

    /**
     * 价格信息内部类
     */
    @Data
    @ApiModel(value = "PriceInfo", description = "价格信息")
    public static class PriceInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "总价")
        private BigDecimal totalPrice;

        @ApiModelProperty(value = "平均每晚价格")
        private BigDecimal avgPrice;

        @ApiModelProperty(value = "入住晚数")
        private Integer nights;

        @ApiModelProperty(value = "价格明细列表")
        private List<PriceDetail> priceDetails;
    }

    /**
     * 价格明细内部类
     */
    @Data
    @ApiModel(value = "PriceDetail", description = "价格明细")
    public static class PriceDetail implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "日期")
        private String date;

        @ApiModelProperty(value = "当日价格")
        private BigDecimal price;

        @ApiModelProperty(value = "价格类型")
        private String priceType;

        @ApiModelProperty(value = "价格类型名称")
        private String priceTypeName;

        @ApiModelProperty(value = "对应的商品ID")
        private Integer productId;

        @ApiModelProperty(value = "商品属性值ID")
        private Integer attrValueId;
    }

    /**
     * 可用性信息内部类
     */
    @Data
    @ApiModel(value = "AvailabilityInfo", description = "可用性信息")
    public static class AvailabilityInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "是否可预订")
        private Boolean available;

        @ApiModelProperty(value = "剩余房间数")
        private Integer remainingRooms;

        @ApiModelProperty(value = "不可预订原因")
        private String unavailableReason;
    }
}
