package com.zbkj.common.response.busines;

import com.zbkj.common.model.business.BizHotspotMgmt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 热点管理
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@ApiModel(value = "BizHotspotResponse", description = "热点管理 返回对象")
public class BizHotspotResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 热点信息
     */
    @ApiModelProperty("热点信息")
    private BizHotspotMgmt hotspotInfo;

    /**
     * 操作事件
     */
    @ApiModelProperty("操作事件: NOT_ALLOCATED: 热点没有分配龙宫币 RECEIVED: 已经领取过龙宫币 CAN_RECEIVE: 可以领取")
    private String operatingEvent;

    /**
     * 本次获取龙宫币数量
     */
    @ApiModelProperty("本次获取龙宫币数量")
    private Integer thisNumber;

    /**
     * 当前龙宫币数量
     */
    @ApiModelProperty("当前龙宫币数量")
    private Integer currentNumber;

    public BizHotspotResponse(String operatingEvent, BizHotspotMgmt hotspotInfo) {
        this.operatingEvent = operatingEvent;
        this.hotspotInfo = hotspotInfo;
    }

    public BizHotspotResponse() {}

} 