package com.zbkj.common.response.foundation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "SysDictItemResponse", description = "字典转化响应对象")
public class SysDictItemResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 字典id
     */
    @ApiModelProperty(value = "字典的id")
    private String dictId;

    /**
     * 字典项文本
     */
    @ApiModelProperty(value = "子项名称")
    private String itemName;

    /**
     * 字典项值
     */
    @ApiModelProperty(value = "子项编码")
    private String itemCode;

    /**
     * 描述
     */
    @ApiModelProperty(value = "子项描述")
    private String itemDesc;

    /**
     * 排序
     */
    @ApiModelProperty(value = "子项排序")
    private Integer itemSort;

    /**
     * 子项是否显示
     */
    @ApiModelProperty(value = "子项是否显示")
    private String itemEnable;

    /**
     * key
     */
    @ApiModelProperty(value = "key")
    private String key;

    /**
     * value
     */
    @ApiModelProperty(value = "value")
    private String value;

    /**
     * label
     */
    @ApiModelProperty(value = "label")
    private String label;

}
