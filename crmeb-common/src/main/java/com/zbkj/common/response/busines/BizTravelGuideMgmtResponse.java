package com.zbkj.common.response.busines;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zbkj.common.model.business.BizTravelGuideAttachment;
import com.zbkj.common.vo.TravelGuideImgVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: BizTravelGuideMgmtResponse
 * @Description: 旅游攻略对象
 * @Author: zlj
 * @Date: 2025-06-10 21:06
 * @Version: 1.0
 **/
@Data
public class BizTravelGuideMgmtResponse {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 攻略类型（字典）
     */
    @ApiModelProperty("攻略类型，参考字典：travelGuide")
    private String guideType;

    /**
     * 攻略封面图
     */
    @ApiModelProperty("攻略封面图")
    private String guideCover;

    /**
     * 攻略名称
     */
    @ApiModelProperty("攻略名称")
    private String guideTitle;

    /**
     * 攻略作者
     */
    @ApiModelProperty("攻略作者")
    private String guideAuthor;

    /**
     * 攻略发布时间
     */
    @TableField("guide_push_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("攻略发布时间")
    private Date guidePushTime;

    /**
     * 置顶（0、1）数据越大显示在最顶上
     */
    @ApiModelProperty("置顶，数据越大显示在最顶上")
    private Integer guideTop;

    /**
     * 攻略是否首页显示
     */
    @ApiModelProperty("攻略是否首页显示,参考字典：yn")
    private String guideIndexShow;

    /**
     * 攻略内容（富文本）
     */
    @ApiModelProperty("攻略内容")
    private String guideContent;

    /**
     * 攻略来源
     */
    @ApiModelProperty("攻略来源")
    private String guideDataSource;

    /**
     * 攻略描述
     */
    @ApiModelProperty("攻略描述")
    private String guideDesc;

    /**
     * 攻略关联的商家id
     */
    @ApiModelProperty("攻略关联的商家id")
    private String guideMerchantId;

    /**
     * 视频
     */
    private String video;

    /**
     * 相册
     */
    private Map<String,String> imgMap;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private String latitude;

    /**
     * 位置
     */
    @ApiModelProperty("位置")
    private String address;

    List<TravelGuideImgVo> attachmentList;
}
