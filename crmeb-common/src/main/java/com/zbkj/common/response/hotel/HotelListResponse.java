package com.zbkj.common.response.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 酒店列表响应对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelListResponse", description = "酒店列表响应对象")
public class HotelListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "酒店ID（商户ID）")
    private Integer hotelId;

    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "酒店图片列表")
    private List<String> hotelImages;

    @ApiModelProperty(value = "酒店主图")
    private String mainImage;

    @ApiModelProperty(value = "星级")
    private Integer starLevel;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "距离（米）")
    private BigDecimal distance;

    @ApiModelProperty(value = "起始价格")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "评分")
    private BigDecimal score;

    @ApiModelProperty(value = "设施标签列表")
    private List<String> facilities;

    @ApiModelProperty(value = "房型数量")
    private Integer roomTypeCount;

    @ApiModelProperty(value = "商户状态")
    private Integer status;

    @ApiModelProperty(value = "商户简介")
    private String intro;
}
