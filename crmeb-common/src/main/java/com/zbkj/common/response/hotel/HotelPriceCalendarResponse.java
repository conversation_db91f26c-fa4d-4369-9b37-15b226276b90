package com.zbkj.common.response.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 酒店价格日历响应对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelPriceCalendarResponse", description = "酒店价格日历响应对象")
public class HotelPriceCalendarResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房间ID")
    private Integer roomId;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "日期价格列表")
    private List<DayPrice> dayPrices;

    /**
     * 日期价格信息
     */
    @Data
    @ApiModel(value = "DayPrice", description = "日期价格信息")
    public static class DayPrice implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "日期(1-31)")
        private Integer day;

        @ApiModelProperty(value = "完整日期(yyyy-MM-dd)")
        private String date;

        @ApiModelProperty(value = "价格")
        private BigDecimal price;

        @ApiModelProperty(value = "日期类型:WORKDAY-工作日,WEEKEND-周末,HOLIDAY-节假日,TRANSFER_WORKDAY-调休工作日")
        private String dateType;

        @ApiModelProperty(value = "日期类型描述")
        private String dateTypeDesc;

        @ApiModelProperty(value = "匹配的策略名称")
        private String strategyName;

        @ApiModelProperty(value = "是否有价格策略")
        private Boolean hasStrategy;
    }
}
