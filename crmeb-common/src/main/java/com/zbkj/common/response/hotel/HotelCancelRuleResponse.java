package com.zbkj.common.response.hotel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 酒店取消规则响应对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelCancelRuleResponse", description = "酒店取消规则响应对象")
public class HotelCancelRuleResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则ID")
    private Integer id;

    @ApiModelProperty(value = "商户ID(酒店ID)")
    private Integer merId;

    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    @ApiModelProperty(value = "提前取消小时数")
    private Integer advanceHours;

    @ApiModelProperty(value = "提前取消时间描述")
    private String advanceTimeDesc;

    @ApiModelProperty(value = "扣费类型:1-按比例,2-固定金额")
    private Integer penaltyType;

    @ApiModelProperty(value = "扣费类型描述")
    private String penaltyTypeDesc;

    @ApiModelProperty(value = "扣费值(比例或金额)")
    private BigDecimal penaltyValue;

    @ApiModelProperty(value = "最小提前取消小时数")
    private Integer minAdvanceHours;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "状态:0-禁用,1-启用")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "规则描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "是否适用于特价房源:0-否,1-是")
    private Integer isForSpecialOffer;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
