<template>
  <div class="login-container">
    <el-steps :active="current" align-center>
      <el-step title="验证账号信息"></el-step>
      <el-step title="修改手机号码"></el-step>
      <el-step title="登录"></el-step>
    </el-steps>
    <el-form
      ref="formInline"
      :model="formInline"
      size="medium"
      :rules="ruleInline"
      class="login-form"
      autocomplete="on"
      label-position="left"
    >
      <template v-if="current === 0">
        <el-form-item prop="account">
          <el-input
            type="text"
            v-model="formInline.account"
            prefix="ios-contact-outline"
            placeholder="请输入当前账号"
            size="large"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            type="password"
            v-model="formInline.password"
            prefix="ios-contact-outline"
            placeholder="请输入密码"
            size="large"
          />
        </el-form-item>
      </template>
      <template v-if="current === 1">
        <el-form-item prop="phone" class="maxInpt">
          <el-input
            type="text"
            v-model="formInline.phone"
            prefix="ios-lock-outline"
            placeholder="请输入新手机号"
            size="large"
          />
        </el-form-item>
        <el-form-item prop="code" class="captcha">
          <div class="acea-row" style="flex-wrap: nowrap">
            <el-input
              ref="username"
              v-model="formInline.code"
              placeholder="验证码"
              name="username"
              type="text"
              tabindex="1"
              autocomplete="off"
              prefix-icon="el-icon-message"
              style="width: 90%"
            />
            <el-button size="mini" :disabled="!this.canClick" @click="cutDown">{{ cutNUm }}</el-button>
          </div>
        </el-form-item>
      </template>
      <template v-if="current === 2">
        <el-form-item prop="phone" class="maxInpt">
          <el-input type="text" v-model="formInline.phone" prefix="ios-contact-outline" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item prop="password" class="maxInpt">
          <el-input type="password" v-model="formInline.password" prefix="ios-lock-outline" placeholder="请输入密码" />
        </el-form-item>
      </template>
      <el-form-item class="maxInpt">
        <el-button
          v-if="current === 0"
          type="primary"
          @click="handleSubmit1('formInline', current)"
          class="mb20 width100"
          >下一步</el-button
        >
        <el-button
          v-if="current === 1 && checkPermi(['platform:one:pass:update:phone'])"
          type="primary"
          @click="handleSubmit2('formInline', current)"
          class="mb20 width100"
          >提交</el-button
        >
        <el-button
          v-if="current === 2 && checkPermi(['platform:one:pass:register'])"
          type="primary"
          @click="handleSubmit('formInline', current)"
          class="mb20 width100"
          >登录</el-button
        >
        <el-button @click="returns('formInline')" class="width100" style="margin-left: 0px">返回</el-button>
      </el-form-item>

      <!--<el-button v-if="current === 0"  size="mini" :loading="loading" type="primary" style="width:100%;margin-bottom:20px;" @click="handleSubmit('formInline')">注册</el-button>-->
      <!--<el-button size="mini" type="primary" style="width:100%;margin-bottom:20px;" @click="changelogo">立即登录</el-button>-->
    </el-form>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { captchaApi, configApi, updateHoneApi, phoneValidatorApi } from '@/api/sms';
import { checkPermi } from '@/utils/permission';
import { validatePhone } from '@/utils/toolsValidate'; // 权限判断函数
export default {
  name: 'forgetPhone',
  props: {
    isIndex: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      cutNUm: '获取验证码',
      canClick: true,
      current: 0,
      formInline: {
        account: '',
        phone: '',
        code: '',
        password: '',
      },
      ruleInline: {
        phone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
        code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        account: [{ required: true, message: '请输入当前账号', trigger: 'blur' }],
      },
    };
  },
  methods: {
    checkPermi,
    // 短信验证码
    cutDown() {
      if (this.formInline.phone) {
        if (!this.canClick) return;
        this.canClick = false;
        this.cutNUm = 60;
        let data = {
          phone: this.formInline.phone,
          types: 1,
        };
        captchaApi(data).then(async (res) => {
          this.$message.success(res.msg);
        });
        let time = setInterval(() => {
          this.cutNUm--;
          if (this.cutNUm === 0) {
            this.cutNUm = '获取验证码';
            this.canClick = true;
            clearInterval(time);
          }
        }, 1000);
      } else {
        this.$message.warning('请填写手机号!');
      }
    },
    handleSubmit1(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          phoneValidatorApi(this.formInline).then(async (res) => {
            this.$message.success('操作成功');
            this.current = 1;
          });
        } else {
          return false;
        }
      });
    },
    handleSubmit2(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          updateHoneApi(this.formInline).then(async (res) => {
            this.$message.success('操作成功');
            this.current = 2;
          });
        } else {
          return false;
        }
      });
    },
    //登录
    handleSubmit(name, num) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          configApi({
            account: this.formInline.account,
            password: this.formInline.password,
          }).then(async (res) => {
            num === 1 ? this.$message.success('原手机号密码正确') : this.$message.success('登录成功');
            num === 1 ? (this.current = 1) : this.$emit('on-Login');
          });
        } else {
          return false;
        }
      });
    },
    returns() {
      this.current === 0 ? this.$emit('gobackPhone') : (this.current = 0);
    },
  },
};
</script>

<style scoped lang="scss">
.login-form {
  flex: 1;
  padding: 32px 0;
  text-align: center;
  width: 384px;
  margin: 0 auto;
  overflow: hidden;
}
.width100 {
  width: 100%;
}

.maxInpt {
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}
.code {
  display: flex;
  align-items: center;
  justify-content: center;
}
.ivu-steps-item:last-child {
  width: unset !important;
}
</style>
