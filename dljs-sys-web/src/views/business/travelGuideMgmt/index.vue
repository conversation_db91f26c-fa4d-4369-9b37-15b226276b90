<template>
  <div class="divBox">
    <el-card
      class="box-card"
      :body-style="{ padding: 0 }"
      :bordered="false"
      shadow="never"
      v-hasPermi="['platform:admin:list']"
    >
      <div class="padding-add">
        <el-form inline size="small" @submit.native.prevent>
          <el-form-item label="攻略类型：">
            <el-select v-model="listPram.guideType" placeholder="请选择攻略类型" clearable class="selWidth">
              <el-option v-for="item in dict.itemList" :key="item.key" :label="item.label" :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="攻略名称：">
            <el-input
              v-model="listPram.guideTitle"
              placeholder="请输入攻略名称"
              class="selWidth"
              size="small"
              @keyup.enter.native="handleGetAdminList"
              clearable />
          </el-form-item>
          <el-form-item label="攻略作者：">
            <el-input
              v-model="listPram.guideAuthor"
              placeholder="请输入攻略作者"
              class="selWidth"
              size="small"
              @keyup.enter.native="handleGetAdminList"
              clearable />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
            <el-button size="small" @click="reset()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card class="box-card mt14" :body-style="{ padding: '20px' }" shadow="never" :bordered="false">
      <el-button size="mini" type="primary" @click="handlerOpenEdit(0)">添加{{apiConfig.name}}</el-button>
      <el-table v-loading="listLoading" class="operation mt20" :data="listData.list" size="small">
        <el-table-column type="index" fixed="left" width="50"/>
        <el-table-column label="攻略类型" prop="guideType" min-width="100">
          <template slot-scope="scope">
            {{ dict.dictMap[scope.row.guideType] }}
          </template>
        </el-table-column>
        <el-table-column label="攻略封面" prop="guideCover" min-width="120">
          <template slot-scope="scope">
            <img v-for="item in getImgUrl(scope.row.guideCover)" :src="item" alt=""/>
          </template>
        </el-table-column>
        <el-table-column label="攻略名称" prop="guideTitle"/>
        <el-table-column label="攻略作者" prop="guideAuthor"/>
        <el-table-column label="发布时间" prop="guidePushTime"/>
        <el-table-column label="置顶顺序" prop="guideTop"/>
        <el-table-column label="是否首页显示" prop="guideIndexShow" v-if="false">
          <template slot-scope="scope">
            {{ dictYn.dictMap[scope.row.guideIndexShow] }}
          </template>
        </el-table-column>
        <el-table-column label="攻略内容" prop="guideContent" v-if="false"/>
        <el-table-column label="攻略描述" prop="guideDesc" v-if="false"/>
        <el-table-column label="商家名称" prop="guideMerchantId" v-if="false"/>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <template v-if="scope.row.isDel">
              <span>-</span>
            </template>
            <template v-else>
              <el-button
                :disabled="scope.row.roles === '1'"
                type="text"
                size="mini"
                @click="handlerOpenEdit(1, scope.row)"
              >编辑
              </el-button
              >
              <el-divider direction="vertical"></el-divider>
              <el-button
                type="text"
                size="mini"
                :disabled="scope.row.roles === '1'"
                @click="handlerOpenDel(scope.row)"
              >删除
              </el-button
              >
            </template>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="listPram.page"
        :page-sizes="constants.page.limit"
        :layout="constants.page.layout"
        :total="listData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    <!--编辑-->
    <el-dialog
      top="40px"
      :visible.sync="editDialogConfig.visible"
      :title="editDialogConfig.isCreate === 0 ?  '创建' + apiConfig.name : '编辑' + apiConfig.name "
      destroy-on-close
      :close-on-click-modal="false"
      width="700px"
      class="dialog-bottom"
    >
      <edit
        ref="editRef"
        v-if="editDialogConfig.visible"
        :is-create="editDialogConfig.isCreate"
        :edit-data="editDialogConfig.editData"
        :dict-data="{dataConfig:  this.dict, dictYn: this.dictYn}"
        @openMap="openMap"
        @hideEditDialog="hideEditDialog"
      />
    </el-dialog>

    <!--编辑-->
    <el-dialog
      top="40px"
      :visible.sync="mapVisible"
      title="选择位置"
      destroy-on-close
      :close-on-click-modal="false"
      width="700px"
      class="dialog-bottom"
    >
      <tencent-map-picker
        :visible.sync="mapVisible"
        @openMap="openMap"
        @confirm="handleLocationConfirm"
      />
    </el-dialog>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { guide as apiConf } from '@/utils/api-config';
import * as dataApi from '@/api/business/common';
import * as dictApi from '@/api/dict';
import edit from './edit';
import {getImgUrl} from "@/utils/imgUtil";
import TencentMapPicker from "@/components/base/TencentMapPicker.vue";

export default {
  // name: "index"
  components: {TencentMapPicker, edit},
  computed: {
    apiConfig() {
      return apiConf || {name: '', path: ''}
    }
  },
  data() {
    return {
      constants: this.$constants,
      listData: {list: []},
      listPram: {
        guideType: null,
        guideTitle: null,
        guideAuthor: null,
        page: 1,
        limit: this.$constants.page.limit[0],
      },
      realName: '',
      roleList: [],
      menuList: [],
      editDialogConfig: {
        visible: false,
        isCreate: 0, // 0=创建，1=编辑
        editData: {},
      },
      //修改密码
      editPassWordDialogConfig: {
        visible: false,
        editData: {},
      },
      adminId: 0, //管理员id
      listLoading: false,
      mapVisible: false,
      keyIndex: 0,
      dict: {
        itemList: [],
        dictMap: {}
      },
      dictYn: {
        itemList: [],
        dictMap: {}
      }
    };
  },
  mounted() {
    this.handleGetAdminList();
    this.initDict();
  },
  methods: {
    getImgUrl,
    async handleGetAdminList() {
      this.listLoading = true;
      // 查询list
      this.listData = await dataApi.list(apiConf.path, this.listPram);
      this.listLoading = false;
    },
    async initDict() {
      this.dict = await dictApi.getDictItems("travelGuide");
      this.dictYn = await dictApi.getDictItems("yn");
    },
    handleSearch() {
      this.listPram.page = 1;
      this.handleGetAdminList();
    },
    handleSizeChange(val) {
      this.listPram.limit = val;
      this.handleGetAdminList();
    },
    handleCurrentChange(val) {
      this.listPram.page = val;
      this.handleGetAdminList();
    },
    handlerOpenDel(rowData) {
      this.$modalSure('确认删除当前数据').then(async () => {
        try {
          await dataApi.remove(apiConf.path, rowData.id);
          this.$message.success('删除数据成功');
          this.hideEditDialog();
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    },
    //重置
    reset() {
      this.listPram.page = 1
      this.listPram.guideType = null;
      this.listPram.guideTitle = null;
      this.listPram.guideAuthor = null;
      this.handleGetAdminList();
    },
    handlerOpenEdit(isCreate, editDate) {
      this.editDialogConfig.editData = editDate;
      this.editDialogConfig.isCreate = isCreate;
      this.editDialogConfig.visible = true;
    },
    hideEditDialog() {
      this.editDialogConfig.visible = false;
      this.handleGetAdminList();
    },
    handleLocationConfirm(location) {
      this.$refs.editRef.handleLocationConfirm(location)
    },
    // 打开，显示地图
    openMap(type) {
      this.mapVisible = type;
    }
  },
};
</script>

<style scoped lang="scss"></style>
