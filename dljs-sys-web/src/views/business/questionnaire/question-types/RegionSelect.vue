<template>
  <base-question :question="question" :index="index" :is-edit="isEdit" @delete="$emit('delete', question.id)">
    <div class="region-container" v-if="isStatistics">
      <el-input
        type="textarea"
        v-model="statisticsOption"
        :rows="8"
        :disabled = "!isEdit"
        placeholder="请输入您的回答"
        class="text-input"
      ></el-input>
    </div>
    <div class="region-container" v-else>
      <div class="top-title" v-if="isEdit">地区选择示例：</div>
      <el-cascader
        v-model="selectedRegions"
        :options="question.regions"
        :disabled = "!isEdit"
        :props="{
          expandTrigger: 'hover',
          value: 'value',
          label: 'text',
          children: 'children'
        }"
        placeholder="请选择地区"
        class="region-select"
        @change="handleRegionChange"
      ></el-cascader>
    </div>
  </base-question>
</template>

<script>
import BaseQuestion from './BaseQuestion.vue';

export default {
  components: {
    BaseQuestion
  },
  props: {
    question: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isStatistics: {
      type: Boolean,
      default: false
    },
    statisticsInfo: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      selectedRegions: [],
      statisticsOption: {}
    }
  },
  mounted() {
    this.initInput();
  },
  methods: {
    initInput() {
      if (this.isStatistics && this.statisticsInfo) {
        let option = this.statisticsInfo.scResult;
        if (option) {
          this.statisticsOption = option;
        }
      }
    },
    handleRegionChange(value) {
      if (value && value.length > 0) {
        this.question.answer = {
          country: value[0],
          province: value[1] || '',
          city: value[2] || ''
        };
      } else {
        this.question.answer = {
          country: '',
          province: '',
          city: ''
        };
      }
    }
  }
}
</script>

<style scoped>
.region-select {
  width: 100%;
}

.top-title {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  margin: 16px 0 8px;
  padding-left: 8px;
  border-left: 3px solid #409EFF;
  line-height: 1.4;
}

.region-container {
  padding: 0 4px;
}

.text-input .el-textarea__inner{
  color: #ea0b30 !important;
}
</style>
