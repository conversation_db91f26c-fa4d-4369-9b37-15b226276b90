<template>
  <base-question :question="question" :index="index" :is-edit="isEdit" @delete="$emit('delete', question.id)">
    <div class="nps-container" v-if="isStatistics">
      <div class="scale-labels">
        <span class="start-label">{{ scale.startLabel }}</span>
        <span class="end-label">{{ scale.endLabel }}</span>
      </div>
      <div class="row">
        <div class="item" v-for="(item, index) in items" :key="index">
          <div class="number-bg">{{ index + 1 }}</div>
          {{ item.count }} 次
        </div>
      </div>
    </div>
    <div class="nps-container" v-else>
      <div class="top-title" v-if="isEdit">自定义NPS量表的提示：</div>
      <el-input class="top-input" v-model="startLabelVal" placeholder="请自定义NPS量表最小值提示" v-if="isEdit">
        <el-button @click="refreshDemo('startLabelVal')" slot="append" icon="el-icon-refresh-left"></el-button>
      </el-input>
      <el-input class="top-input" style="float: right" v-model="endLabelVal" placeholder="请自定义NPS量表最大值提示" v-if="isEdit">
        <el-button @click="refreshDemo('endLabelVal')" slot="append" icon="el-icon-refresh-left"></el-button>
      </el-input>
      <div class="top-title" v-if="isEdit">自定义NPS量表的最大值：</div>
      <el-input v-model="endVal" placeholder="请自定义NPS量表的最大值" v-if="isEdit">
        <el-button type="number" @click="refreshDemo('endVal')" slot="append" icon="el-icon-refresh-left"></el-button>
      </el-input>
      <div class="top-title" v-if="isEdit">NPS量表示例：</div>
      <div class="scale-labels">
        <span class="start-label">{{ scale.startLabel }}</span>
        <span class="end-label">{{ scale.endLabel }}</span>
      </div>
      <el-slider
        v-model="question.answer"
        :min="scale.start"
        :max="scale.end"
        :step="1"
        :disabled = "!isEdit"
        show-stops
        class="nps-slider"
      ></el-slider>
      <div class="selected-value" v-if="question.answer !== null">
        当前选择: {{ question.answer }}
      </div>
    </div>
  </base-question>
</template>

<script>
import BaseQuestion from './BaseQuestion.vue';

export default {
  components: {
    BaseQuestion
  },
  props: {
    question: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isStatistics: {
      type: Boolean,
      default: false
    },
    statisticsInfo: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      scale: {},
      startVal: 0,
      endVal: 10,
      startLabelVal: '',
      endLabelVal: '',
      items: ['Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5']
    }
  },
  mounted() {
    this.initInput();
  },
  methods: {
    initInput() {
      if (this.question.scale) {
        this.startVal = this.question.scale.start;
        this.endVal = this.question.scale.end;
        this.startLabelVal = this.question.scale.startLabel;
        this.endLabelVal = this.question.scale.endLabel;
        this.scale = {...this.question.scale}
      }
      if (this.isStatistics) {
        this.getStatistics();
      }
    },
    refreshDemo(type) {
      if (type === 'startVal')      this.scale.start = this.startVal;
      if (type === 'endVal')        this.scale.end = this.endVal;
      if (type === 'startLabelVal') this.scale.startLabel = this.startLabelVal;
      if (type === 'endLabelVal')   this.scale.endLabel = this.endLabelVal;
      this.question.scale = {...this.scale};
    },
    getStatistics() {
      let op = {};
      if (this.statisticsInfo) {
        let option = this.statisticsInfo.scResult;
        if (option) {
          for (let opt of JSON.parse(option)) {
            op[opt.value] = opt;
          }
        }
      }

      let st = this.question.scale.start;
      let en = this.question.scale.end;
      let item = [];
      for (let i = st ; i <= en; i++ ) {
        item.push(op[i]);
      }
      this.items = item
    }
  }
}
</script>

<style scoped>
.scale-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  color: #606266;
}

.nps-slider {
  margin: 20px 0;
}

.selected-value {
  text-align: center;
  color: #409EFF;
  font-weight: bold;
  margin-top: 10px;
}

.top-title {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  margin: 16px 0 8px;
  padding-left: 8px;
  border-left: 3px solid #409EFF;
  line-height: 1.4;
}

.top-input {
  width: 40%;
}

.nps-container {
  padding: 0 4px;
}




.row {
  display: flex;
  width: 100%;
}

.item {
  flex: 1;
  padding: 15px;
  background-color: #f0f0f0;
  border-radius: 4px;
  text-align: center;
  margin: 0 5px;
  position: relative;
  overflow: hidden;
}

.number-bg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 120px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.1);
  z-index: 0;
  user-select: none;
}

.item:first-child {
  margin-left: 0;
}

.item:last-child {
  margin-right: 0;
}
</style>
