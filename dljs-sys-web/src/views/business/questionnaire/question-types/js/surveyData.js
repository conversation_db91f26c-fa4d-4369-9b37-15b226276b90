// 模拟地区数据 (简化版)
const regions = [
  { value: 'CN', text: '中国', children: [
      { value: 'GD', text: '广东', children: [
          { value: 'GZ', text: '广州' },
          { value: 'SZ', text: '深圳' }
        ]},
      { value: 'BJ', text: '北京', children: [
          { value: 'BJ', text: '北京市' }
        ]},
    ]},
  { value: 'US', text: '美国', children: [
      { value: 'CA', text: '加州' },
      { value: 'NY', text: '纽约州' }
    ]}
];

export const surveyData = {
  title: '用户体验调查问卷',
  description: '请花几分钟填写本问卷，帮助我们改进产品和服务。',
  questions:[],
  // questions: [
  //   {
  //     id: 'q_single',
  //     type: 'single-choice',
  //     text: '您经常使用的浏览器是？',
  //     required: true,
  //     options: [
  //       { value: 'chrome', text: 'Chrome' },
  //       { value: 'firefox', text: 'Firefox' },
  //       { value: 'safari', text: 'Safari' },
  //       { value: 'edge', text: 'Edge' },
  //       { value: 'other', text: '其他' },
  //     ],
  //     answer: null // 用于存储用户答案
  //   },
  //   {
  //     id: 'q_multiple',
  //     type: 'multiple-choice',
  //     text: '您关注的编程语言有哪些？(可多选)',
  //     required: false,
  //     options: [
  //       { value: 'js', text: 'JavaScript' },
  //       { value: 'python', text: 'Python' },
  //       { value: 'java', text: 'Java' },
  //       { value: 'csharp', text: 'C#' },
  //       { value: 'go', text: 'Go' },
  //     ],
  //     answer: [] // 用于存储用户答案
  //   },
  //   {
  //     id: 'q_dropdown',
  //     type: 'dropdown',
  //     text: '您的教育程度是？',
  //     required: true,
  //     options: [
  //       { value: '', text: '请选择' },
  //       { value: 'highschool', text: '高中及以下' },
  //       { value: 'bachelor', text: '本科' },
  //       { value: 'master', text: '硕士' },
  //       { value: 'doctor', text: '博士及以上' },
  //     ],
  //     answer: '' // 用于存储用户答案
  //   },
  //   {
  //     id: 'q_text',
  //     type: 'text',
  //     text: '请写下您对我们产品的建议：',
  //     required: false,
  //     answer: '' // 用于存储用户答案
  //   },
  //   {
  //     id: 'q_nps',
  //     type: 'nps-scale',
  //     text: '您有多大可能性向朋友或同事推荐我们的产品？',
  //     required: true,
  //     scale: { start: 0, end: 10, startLabel: '非常不可能', endLabel: '非常可能' },
  //     answer: null // 用于存储用户答案
  //   },
  //   {
  //     id: 'q_matrix_single',
  //     type: 'matrix-single',
  //     text: '请评价以下各项服务的重要性：',
  //     required: true,
  //     rows: [
  //       { value: 'service_a', text: '服务 A' },
  //       { value: 'service_b', text: '服务 B' },
  //       { value: 'service_c', text: '服务 C' },
  //     ],
  //     cols: [
  //       { value: '1', text: '不重要' },
  //       { value: '2', text: '一般' },
  //       { value: '3', text: '重要' },
  //       { value: '4', text: '非常重要' },
  //     ],
  //     answer: {} // 用于存储用户答案，格式如 { service_a: '3', service_b: '2' }
  //   },
  //   {
  //     id: 'q_matrix_multiple',
  //     type: 'matrix-multiple',
  //     text: '您使用过以下哪些产品特性？',
  //     required: false,
  //     rows: [
  //       { value: 'feature_x', text: '特性 X' },
  //       { value: 'feature_y', text: '特性 Y' },
  //       { value: 'feature_z', text: '特性 Z' },
  //     ],
  //     cols: [
  //       { value: 'used', text: '使用过' },
  //       { value: 'often_use', text: '经常使用' },
  //       { value: 'like', text: '很喜欢' },
  //     ],
  //     answer: {} // 用于存储用户答案，格式如 { feature_x: ['used', 'like'], feature_y: ['often_use'] }
  //   },
  //   {
  //     id: 'q_region',
  //     type: 'region-select',
  //     text: '请选择您所在的地区：',
  //     required: true,
  //     regions: regions, // 地区数据
  //     answer: { country: '', province: '', city: '' } // 用于存储用户答案
  //   }
  // ],
};
