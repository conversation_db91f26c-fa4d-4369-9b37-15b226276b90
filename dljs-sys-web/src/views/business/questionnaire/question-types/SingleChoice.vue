<template>
  <base-question :question="question" :index="index" :is-edit="isEdit" @delete="$emit('delete', question.id)">
    <div class="options-list">
      <div class="top-title" v-if="isEdit">自定义单选题选项(使用逗号分隔)：</div>
      <el-input class="top-input" v-model="inputVal" placeholder="请自定义单选题选项" v-if="isEdit">
        <el-button @click="refreshDemo" slot="append" icon="el-icon-refresh-left"></el-button>
      </el-input>
      <div class="top-title" v-if="isEdit">单选题示例：</div>
      <el-radio-group v-model="question.answer" :disabled = "!isEdit">
        <el-radio
          v-for="option in options"
          :key="option.value"
          :label="option.value"
          class="option-item"
        >
          {{ option.text }} <!-- <span style="color: #ea0b30" v-if="!isEdit">(30%)</span> -->
          <span style="color: red;" v-if="statisticsOption[option.value]">  {{statisticsOption[option.value].count}}次 </span>
        </el-radio>
      </el-radio-group>
    </div>
  </base-question>
</template>

<script>
import BaseQuestion from './BaseQuestion.vue';

export default {
  components: {
    BaseQuestion
  },
  props: {
    question: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isStatistics: {
      type: Boolean,
      default: false
    },
    statisticsInfo: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      options: [],
      inputVal: '',
      statisticsOption: {}
    }
  },
  mounted() {
    this.initInput();
  },
  methods: {
    initInput() {
      if (this.question.options && this.question.options.length > 0) {
        // 在可以编辑的时候，需要把text的内容转为input的value，中间使用逗号分隔
        for (let option of this.question.options) {
          this.inputVal += option.text + ',';
        }
        this.refreshDemo();
      }
      if (this.isStatistics && this.statisticsInfo) {
        let option = this.statisticsInfo.scResult;
        if (option) {
          let op = {};
          for (let opt of JSON.parse(option)) {
            op[opt.value] = opt;
          }
          this.statisticsOption = op;
        }
      }
    },
    refreshDemo() {
      let invalid = this.inputVal.replaceAll('，', ',');
      let inArray = invalid.split(',');
      let options = [];
      if (inArray && inArray.length > 0) {
        for (let [index, iv] of inArray.entries()) {
          if (iv) {
            options.push({value: index + 1, text: iv.trim()})
          }
        }
      }
      this.options = options;
      this.question.options = options;
    }
  }
}
</script>

<style scoped>
.option-item {
  display: block;
  margin-bottom: 10px;
  margin-right: 0;
}

.option-item:last-child {
  margin-bottom: 0;
}

.top-title {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  margin: 16px 0 8px;
  padding-left: 8px;
  border-left: 3px solid #409EFF;
  line-height: 1.4;
}

.options-list {
  padding: 0 4px;
}
</style>
