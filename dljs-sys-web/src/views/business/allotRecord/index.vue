<template>
    <div class="divBox">
      <el-card
        class="box-card"
        :body-style="{ padding: 0 }"
        :bordered="false"
        shadow="never"
        v-hasPermi="['platform:admin:list']"
      >
        <div class="padding-add">
          <el-form inline size="small" @submit.native.prevent>
            <el-form-item label="随机分配热点">
              <el-select v-model="listPram.allotRandomHotspot" placeholder="请选择随机分配热点" clearable class="selWidth">
                <el-option v-for="item in dictType.itemList" :key="item.key" :label="item.text" :value="item.value"/>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
              <!-- <el-button size="small" @click="reset()">重置</el-button> -->
              <el-button size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="box-card mt14" :body-style="{ padding: '20px' }" shadow="never" :bordered="false">
        <el-table v-loading="listLoading" class="operation mt20" :data="listData.list" size="small">
          <el-table-column type="index" fixed="left" width="50"/>
          <el-table-column label="随机分配热点" prop="allotRandomHotspot" min-width="150">
            <template slot-scope="scope">
              {{ dictType.dictMap[scope.row.allotRandomHotspot] }}
            </template>
          </el-table-column>
          <el-table-column label="随机分配数量" prop="allotRandomNumber" min-width="80"/>
          <el-table-column label="被领取的数量" prop="allotReceiveNumber" min-width="80"/>
          <el-table-column label="分配日期" prop="allotDate"/>
          <el-table-column label="操作" width="120" fixed="right">
            <template slot-scope="scope">
              <template v-if="scope.row.isDel">
                <span>-</span>
              </template>
              <template v-else>
                <el-button
                  :disabled="scope.row.roles === '1'"
                  type="text"
                  size="mini"
                  @click="handlerOpenEdit(1, scope.row)"
                  v-hasPermi="['platform:admin:update', 'platform:admin:info']"
                >编辑
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          :current-page="listPram.page"
          :page-sizes="constants.page.limit"
          :layout="constants.page.layout"
          :total="listData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-card>
      <!--编辑-->
      <el-dialog
        top="40px"
        :visible.sync="editDialogConfig.visible"
        :title="editDialogConfig.isCreate === 0 ?  '创建' + apiConfig.name : '编辑' + apiConfig.name "
        destroy-on-close
        :close-on-click-modal="false"
        width="500px"
        class="dialog-bottom"
      >
        <edit
          v-if="editDialogConfig.visible"
          :is-create="editDialogConfig.isCreate"
          :edit-data="editDialogConfig.editData"
          :dict-data="{dictType:  this.dictType}"
          @hideEditDialog="hideEditDialog"
        />
      </el-dialog>
    </div>
  </template>

  <script>
  // +---------------------------------------------------------------------
  // | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
  // +---------------------------------------------------------------------
  // | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
  // +---------------------------------------------------------------------
  // | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
  // +---------------------------------------------------------------------
  // | Author: CRMEB Team <<EMAIL>>
  // +---------------------------------------------------------------------
  import { record as apiConf } from '@/utils/api-config';
  import * as dataApi from '@/api/business/common';
  import * as dictApi from '@/api/dict';
  import * as hotspotApi from '@/api/business/hotspot';
  import edit from './edit';

  export default {
    // name: "index"
    components: {edit},
    computed: {
      apiConfig() {
        return apiConf || {name: '', path: ''}
      }
    },
    data() {
      return {
        constants: this.$constants,
        listData: {list: []},
        listPram: {
          page: 1,
          limit: this.$constants.page.limit[0],
        },
        realName: '',
        roleList: [],
        menuList: [],
        editDialogConfig: {
          visible: false,
          isCreate: 0, // 0=创建，1=编辑
          editData: {},
        },
        //修改密码
        editPassWordDialogConfig: {
          visible: false,
          editData: {},
        },
        adminId: 0, //管理员id
        listLoading: false,
        keyIndex: 0,
        dictType: {
          itemList: [],
          dictMap: {}
        },
      };
    },
    mounted() {
      this.handleGetAdminList();
      this.initDict();
    },
    methods: {
      async handleGetAdminList() {
        this.listLoading = true;
        // 查询list
        this.listData = await dataApi.list(apiConf.path, this.listPram);
        this.listLoading = false;
      },
      async initDict() {
        // 不从字典获取
        // this.dict = await dictApi.getDictItems('hotspot');
        // 根据景区热点获取
        this.dictType = await hotspotApi.dataList();
      },
      handleSearch() {
        this.listPram.page = 1;
        this.handleGetAdminList();
      },
      handleSizeChange(val) {
        this.listPram.limit = val;
        this.handleGetAdminList();
      },
      handleCurrentChange(val) {
        this.listPram.page = val;
        this.handleGetAdminList();
      },
      handlerOpenDel(rowData) {
        this.$modalSure('确认删除当前数据').then(() => {
            dataApi.remove(rowData.id).then((data) => {
          this.$message.success('删除数据成功');
          this.handleGetAdminList();
        });
      });
      },
      //重置
      resetQuery() {
      this.listPram = {
        page: 1,
        limit: this.$constants.page.limit[0],
      };
      this.handleGetAdminList();
    },
      handlerOpenEdit(isCreate, editDate) {
        this.editDialogConfig.editData = editDate;
        this.editDialogConfig.isCreate = isCreate;
        this.editDialogConfig.visible = true;
      },
      hideEditDialog() {
        this.editDialogConfig.visible = false;
        this.handleGetAdminList();
      },
    },
  };
  </script>

  <style scoped lang="scss"></style>
