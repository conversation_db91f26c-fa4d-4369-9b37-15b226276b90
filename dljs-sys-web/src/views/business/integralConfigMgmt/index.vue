<template>
  <div class="divBox">
    <el-card class="box-card mt14" :body-style="{ padding: '20px' }" shadow="never" :bordered="false">
      <el-button size="mini" type="primary" @click="handlerOpenEdit(0)">添加</el-button>
      <el-table v-loading="listLoading" class="operation mt20" :data="listData.list" size="small">
        <el-table-column type="index" fixed="left" width="50" />
        <el-table-column label="随机分配热点" prop="integralRandomHotspot">
          <template slot-scope="scope">
            <span v-for="item in scope.row.integralRandomHotspot.split(',')">
              {{ dict.dictMap[item] }}<br/>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="随机数量(个/天)" prop="integralRandomNumber" />
        <el-table-column label="固定分配热点" prop="integralFixedHotspot">
          <template slot-scope="scope">
            <span v-for="item in scope.row.integralFixedHotspot.split(',')">
              {{ dict.dictMap[item] }}<br/>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="固定数量(个)" prop="integralFixedNumber" v-if="false"/>
        <el-table-column label="问卷奖励(个)" prop="integralSurvey" />
        <el-table-column label="消费奖励(%)" prop="integralConsume" />
        <!-- <el-table-column label="是否启用" prop="integralEnable">
            <template slot-scope="scope">
            {{ dictYn.dictMap[scope.row.integralEnable] }}
          </template>
          </el-table-column> -->
        <el-table-column label="是否启用" min-width="80">
          <template slot-scope="scope">
            <el-switch
              style="width: 48px"
              v-model="scope.row.integralEnable"
              :active-value="true"
              :inactive-value="false"
              active-text="是"
              inactive-text="否"
              @change="onchangeIsShow(scope.row)"
            />

          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <template v-if="scope.row.isDel">
              <span>-</span>
            </template>
            <template v-else>
              <el-button
                :disabled="scope.row.integralEnable"
                type="text"
                size="mini"
                @click="handlerOpenEdit(1, scope.row)"
                v-hasPermi="['platform:admin:update', 'platform:admin:info']"
                >编辑
              </el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button
                type="text"
                size="mini"
                :disabled="scope.row.integralEnable"
                @click="handlerOpenDel(scope.row)"
                v-hasPermi="['platform:admin:delete']"
                >删除
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="listPram.page"
        :page-sizes="constants.page.limit"
        :layout="constants.page.layout"
        :total="listData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    <!--编辑-->
    <el-dialog
      top="40px"
      :visible.sync="editDialogConfig.visible"
      :title="editDialogConfig.isCreate === 0 ? '创建龙宫币配置' : '编辑龙宫币配置'"
      destroy-on-close
      :close-on-click-modal="false"
      width="700px"
      class="dialog-bottom"
    >
      <edit
        v-if="editDialogConfig.visible"
        :is-create="editDialogConfig.isCreate"
        :edit-data="editDialogConfig.editData"
        :dict-data="{ dataConfig: this.dict }"
        @hideEditDialog="hideEditDialog"
      />
    </el-dialog>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------

import * as dataApi from '@/api/business/integralConfigMgmt';
import * as dictApi from '@/api/dict';
import * as hotspotApi from '@/api/business/hotspot';
import edit from './edit';

export default {
  // name: "index"
  components: { edit },
  data() {
    return {
      constants: this.$constants,
      listData: { list: [] },
      listPram: {
        page: 1,
        limit: this.$constants.page.limit[0],
      },
      integralEnable: 1,
      realName: '',
      editDialogConfig: {
        visible: false,
        isCreate: 0, // 0=创建，1=编辑
        editData: {},
      },
      adminId: 0, //管理员id
      listLoading: false,
      keyIndex: 0,
      dict: {
        itemList: [],
        dictMap: {},
      },
      dictYn: {
        itemList: [],
        dictMap: {},
      },
    };
  },
  mounted() {
    this.handleGetAdminList();
    this.initDict();
  },
  methods: {
    async handleGetAdminList() {
      this.listLoading = true;
      // 查询list
      dataApi.getList(this.listPram).then((data) => {
        this.listData = data;
      });
      this.listLoading = false;
    },
    async initDict() {
      // 不从字典获取
      // this.dict = await dictApi.getDictItems('hotspot');
      // 根据景区热点获取
      this.dict = await hotspotApi.dataList();
    },
    handleSearch() {
      this.listPram.page = 1;
      this.handleGetAdminList();
    },
    handleSizeChange(val) {
      this.listPram.limit = val;
      this.handleGetAdminList();
    },
    handleCurrentChange(val) {
      this.listPram.page = val;
      this.handleGetAdminList();
    },
    handlerOpenDel(rowData) {
      this.$modalSure('确认删除当前数据').then(() => {
        dataApi.remove(rowData.id).then((data) => {
          this.$message.success('删除数据成功');
          this.handleGetAdminList();
        });
      });
    },
    //重置
    reset() {
      this.listPram.realName = '';
      this.realName = '';
      this.listPram.page = 1;
      this.handleGetAdminList();
    },

    //是否启用
    onchangeIsShow(row) {
      row.integralEnable = !row.integralEnable;
      const title = !row.integralEnable ? '是否启用配置' : '是否关闭配置';
      this.$modalSure(title).then(() => {
        dataApi.updateIntegralEnable(row).then(() => {
          // row.integralEnable = !row.integralEnable;
          this.handleGetAdminList();
        });
      });



    },
    handlerOpenEdit(isCreate, editDate) {
      this.editDialogConfig.editData = editDate;
      this.editDialogConfig.isCreate = isCreate;
      this.editDialogConfig.visible = true;
    },
    hideEditDialog() {
      this.editDialogConfig.visible = false;
      this.handleGetAdminList();
    },
  },
};
</script>

<style scoped lang="scss"></style>
