<template>
  <div>
    <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="id：" prop="id" v-show="false">
        <el-input v-model.trim="pram.id" placeholder="id"/>
      </el-form-item>
      <el-form-item label="公告名称：" prop="noticeTitle">
        <el-input v-model.trim="pram.noticeTitle" placeholder="请输入攻略名称"/>
      </el-form-item>
      <el-form-item label="公告发布时间：" prop="noticePushTime">
        <el-date-picker
          v-model.trim="pram.noticePushTime"
          class="item-model"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择发布时间"/>
      </el-form-item>
      <el-form-item label="公告描述：" prop="noticeDesc">
        <el-input type="textarea" v-model.trim="pram.noticeDesc" placeholder="请输入公告描述"/>
      </el-form-item>
      <el-form-item label="公告内容：" prop="noticeContent">
        <Tinymce height="200px" v-model="pram.noticeContent" :key="keyIndex"></Tinymce>
      </el-form-item>
      <el-form-item></el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="handlerSubmit('pram')"
        v-hasPermi="['platform:admin:save', 'platform:admin:update']"
      >
        {{ isCreate === 0 ? '确定' : '更新' }}
      </el-button
      >
    </div>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { Debounce } from '@/utils/validate';
import Tinymce from "@/components/Tinymce/index.vue";
import { notice as api } from '@/utils/api-config';
import * as dataApi from '@/api/business/common';
import { formatDates } from '@/utils/index';

export default {
  components: { Tinymce },
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: () => {
        return {rules: []};
      },
    },
    dictData: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    }
  },
  computed: {},
  data() {
    return {
      constants: this.$constants,
      pram: {
        id: null,
        noticeTitle: null,
        noticePushTime: formatDates(new Date(), "yyyy-MM-dd hh:mm:ss"),
        noticeDesc: null,
        noticeContent: null,
      },
      roleList: [],
      rules: {
        noticeTitle: [{required: true, message: '请输入公告名称', trigger: ['blur', 'change']}],
        noticePushTime: [{required: true, message: '请选择公告发布时间', trigger: ['blur', 'change']}],
        noticeContent: [{required: true, message: '请输入公告内容', trigger: ['blur', 'change']}],
      },
      keyIndex: '0',
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      this.pram = {...this.editData}
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息')
          if (this.isCreate === 0) {
            this.handlerSave();
          } else {
            this.handlerEdit();
          }
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    }),
    async handlerSave() {
      await dataApi.save(api.path, this.pram);
      this.$message.success(`创建${api.name}成功`);
      this.$emit('hideEditDialog');
    },
    async handlerEdit() {
      await dataApi.update(api.path, this.pram);
      this.$message.success(`更新${api.name}数据成功`);
      this.$emit('hideEditDialog');
    },
    // 点击商品图
    modalPicTap(multiple) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
        function (img) {
          if (!img) return;
          _this.editPram.icon = img[0].sattDir;
        },
        multiple,
        'store',
      );
    },
  },
};
</script>

<style scoped>
.item-model {
  width: 100%;
}
</style>
